import { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { FaUser, FaCamera } from 'react-icons/fa';
import { API_URL } from '../../config/env.js';
import { getAvatarUrl } from '../../utils/mediaUtils';
import { useAuth } from '../../context/AuthContext';

const SettingsSection = ({ user }) => {
  const { refreshUser } = useAuth(); // Get auth context to refresh user data
  const [profileData, setProfileData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    bio: user?.bio || '',
    profileImage: user?.profileImage || null
  });

  const fileInputRef = useRef(null);
  
  // Update profile data when user data changes (only when user ID changes or initial load)
  useEffect(() => {
    if (user) {
      setProfileData({
        username: user.username || '',
        email: user.email || '',
        bio: user.bio || '',
        profileImage: user.profileImage || null
      });
    }
  }, [user?.id, user?.username, user?.email, user?.bio, user?.profileImage]); // Only update when specific fields change

  // Add password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  // Add feedback state
  const [feedback, setFeedback] = useState({
    profile: { message: '', type: '' },
    password: { message: '', type: '' }
  });
  
  const [loading, setLoading] = useState({
    profile: false,
    password: false
  });
  
  // Handle profile form input changes
  const handleProfileChange = (e) => {
    setProfileData({
      ...profileData,
      [e.target.id]: e.target.value
    });
  };

  // Handle image upload
  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setFeedback({
          ...feedback,
          profile: {
            message: 'Please select a valid image file',
            type: 'error'
          }
        });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setFeedback({
          ...feedback,
          profile: {
            message: 'Image size must be less than 5MB',
            type: 'error'
          }
        });
        return;
      }

      // Set loading state for image upload
      setLoading({...loading, profile: true});

      try {
        // Create FormData for file upload
        const formData = new FormData();
        formData.append('profileImage', file);

        // Upload image to backend
        const response = await fetch(`${API_URL}/api/users/upload-profile-image`, {
          method: 'POST',
          credentials: 'include',
          body: formData
        });

        const data = await response.json();

        if (response.ok) {
          // Update profile data with new image URL
          setProfileData({
            ...profileData,
            profileImage: data.imageUrl
          });

          // Show success message
          setFeedback({
            ...feedback,
            profile: {
              message: 'Profile image updated successfully',
              type: 'success'
            }
          });

          // Refresh user data in auth context
          try {
            await refreshUser();
          } catch (refreshError) {
            console.error('Error refreshing user data:', refreshError);
          }
        } else {
          setFeedback({
            ...feedback,
            profile: {
              message: data.message || 'Failed to upload image',
              type: 'error'
            }
          });
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        setFeedback({
          ...feedback,
          profile: {
            message: 'An error occurred while uploading image',
            type: 'error'
          }
        });
      } finally {
        setLoading({...loading, profile: false});
      }
    }
  };

  // Trigger file input
  const triggerImageUpload = () => {
    fileInputRef.current?.click();
  };
  
  // Handle password form input changes
  const handlePasswordChange = (e) => {
    setPasswordData({
      ...passwordData,
      [e.target.id]: e.target.value
    });
  };
  
  // Submit profile update
  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setLoading({...loading, profile: true});
    
    // Username validation
    if (profileData.username.trim().length < 3) {
      setFeedback({
        ...feedback,
        profile: {
          message: 'Username must be at least 3 characters long',
          type: 'error'
        }
      });
      setLoading({...loading, profile: false});
      return;
    }
    
    try {
      // Send request to update profile
      const response = await fetch(`${API_URL}/api/users/profile`, {
        method: 'PUT',
        credentials: 'include', // Use cookies for authentication
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: profileData.username,
          bio: profileData.bio
        })
      });
      
      // Get response text first to see what's being returned
      const responseText = await response.text();
      
      // Try to parse as JSON if there's content
      let data;
      try {
        data = responseText ? JSON.parse(responseText) : {};
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        throw new Error('Server returned an invalid response');
      }
      
      if (response.ok) {
        // Update successful
        setFeedback({
          ...feedback,
          profile: {
            message: 'Profile updated successfully',
            type: 'success'
          }
        });

        // Refresh user data in the auth context
        try {
          await refreshUser();
        } catch (refreshError) {
          console.error('Error refreshing user data:', refreshError);
        }
      } else {
        // Error occurred
        console.error('Profile update failed:', data);
        setFeedback({
          ...feedback,
          profile: {
            message: data.message || 'Failed to update profile',
            type: 'error'
          }
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setFeedback({
        ...feedback,
        profile: {
          message: 'An error occurred while updating profile: ' + error.message,
          type: 'error'
        }
      });
    } finally {
      setLoading({...loading, profile: false});
    }
  };
  
  // Submit password update
  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setLoading({...loading, password: true});
    
    // Validate passwords
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setFeedback({
        ...feedback,
        password: { 
          message: 'New passwords do not match', 
          type: 'error' 
        }
      });
      setLoading({...loading, password: false});
      return;
    }
    
    if (passwordData.newPassword.length < 6) {
      setFeedback({
        ...feedback,
        password: {
          message: 'Password must be at least 6 characters long',
          type: 'error'
        }
      });
      setLoading({...loading, password: false});
      return;
    }
    
    try {
      // Send request to backend
      const response = await fetch(`${API_URL}/auth/change-password`, {
        method: 'PUT',
        credentials: 'include', // Use cookies for authentication
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Success
        setFeedback({
          ...feedback,
          password: { 
            message: 'Password updated successfully', 
            type: 'success' 
          }
        });
        
        // Clear password fields
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        // Error
        setFeedback({
          ...feedback,
          password: { 
            message: data.message || 'Failed to update password', 
            type: 'error' 
          }
        });
      }
    } catch {
      setFeedback({
        ...feedback,
        password: { 
          message: 'An error occurred. Please try again.', 
          type: 'error' 
        }
      });
    } finally {
      setLoading({...loading, password: false});
    }
  };
  
  return (
    <section className="space-y-8">
      <h1 className="text-3xl font-bold text-white">Account Settings</h1>
      
      {/* Profile Information Form */}
      <form className="space-y-6" onSubmit={handleProfileSubmit}>
        <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
          <h2 className="text-xl font-semibold text-white mb-6 pb-3 border-b border-gray-600">Profile Information</h2>

          <div className="space-y-4">
            {/* Profile Image Upload */}
            <div className="flex flex-col items-center mb-6">
              <div className="relative">
                <div className="w-32 h-32 bg-gray-700 rounded-full flex items-center justify-center mb-4 overflow-hidden border-2 border-gray-600">
                  <img
                    src={getAvatarUrl(profileData.profileImage, user.id)}
                    alt={user.username}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  type="button"
                  onClick={triggerImageUpload}
                  className="absolute bottom-2 right-2 bg-red-500 hover:bg-red-600 text-white p-2 rounded-full transition-colors duration-200"
                >
                  <FaCamera className="text-sm" />
                </button>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              <p className="text-gray-400 text-sm text-center">Click the camera icon to change your profile picture</p>
            </div>
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input 
                type="text" 
                id="username" 
                value={profileData.username} 
                onChange={handleProfileChange}
                minLength={3}
                required
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
              <input 
                type="email" 
                id="email" 
                value={profileData.email} 
                readOnly
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-gray-400 cursor-not-allowed"
                title="Email address cannot be changed"
              />
              <small className="text-gray-500 text-xs mt-1 block">Email address cannot be changed</small>
            </div>
            
            <div>
              <label htmlFor="bio" className="block text-sm font-medium text-gray-300 mb-2">Bio</label>
              <textarea 
                id="bio" 
                rows="4"
                placeholder="Tell others about yourself..."
                value={profileData.bio}
                onChange={handleProfileChange}
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200 resize-none"
              ></textarea>
            </div>
          </div>
          
          {feedback.profile.message && (
            <div className={`mt-4 p-3 rounded-lg ${
              feedback.profile.type === 'success' 
                ? 'bg-green-500/20 border border-green-500/50 text-green-300' 
                : 'bg-red-500/20 border border-red-500/50 text-red-300'
            }`}>
              {feedback.profile.message}
            </div>
          )}
          
          <div className="mt-6">
            <button 
              type="submit" 
              className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading.profile}
            >
              {loading.profile ? 'Saving...' : 'Save Profile'}
            </button>
          </div>
        </div>
      </form>
      
      {/* Password Form */}
      <form className="space-y-6" onSubmit={handlePasswordSubmit}>
        <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
          <h2 className="text-xl font-semibold text-white mb-6 pb-3 border-b border-gray-600">Password</h2>

          {/* Hidden username field for accessibility and password managers */}
          <input
            type="text"
            name="username"
            value={user?.username || ''}
            autoComplete="username"
            style={{ display: 'none' }}
            readOnly
            tabIndex={-1}
            aria-hidden="true"
          />

          <div className="space-y-4">
            <div>
              <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
              <input
                type="password"
                id="currentPassword"
                value={passwordData.currentPassword}
                onChange={handlePasswordChange}
                required
                autoComplete="current-password"
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
            
            <div>
              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-300 mb-2">New Password</label>
              <input
                type="password"
                id="newPassword"
                value={passwordData.newPassword}
                onChange={handlePasswordChange}
                required
                minLength={6}
                autoComplete="new-password"
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
            
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
              <input
                type="password"
                id="confirmPassword"
                value={passwordData.confirmPassword}
                onChange={handlePasswordChange}
                required
                autoComplete="new-password"
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
          </div>
          
          {feedback.password.message && (
            <div className={`mt-4 p-3 rounded-lg ${
              feedback.password.type === 'success' 
                ? 'bg-green-500/20 border border-green-500/50 text-green-300' 
                : 'bg-red-500/20 border border-red-500/50 text-red-300'
            }`}>
              {feedback.password.message}
            </div>
          )}
          
          <div className="mt-6">
            <button 
              type="submit" 
              className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading.password}
            >
              {loading.password ? 'Updating...' : 'Update Password'}
            </button>
          </div>
        </div>
      </form>
      
      <div className="bg-red-500/10 border border-red-500/30 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-red-400 mb-4">Danger Zone</h3>
        <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 mb-3">
          Delete Account
        </button>
        <p className="text-gray-400 text-sm">This action cannot be undone. All your data will be permanently deleted.</p>
      </div>
    </section>
  );
};

SettingsSection.propTypes = {
  user: PropTypes.object.isRequired
};

export default SettingsSection;
